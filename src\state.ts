import { Annotation } from "@langchain/langgraph";

// Define the structure of the state object that will be passed between nodes in the LangGraph workflow.
// Each property represents a key piece of data used by the agents during execution.
export const DependencyRiskAnnotation = Annotation.Root({
    // Path to the file being analyzed, typically a package.json file.
    filePath: Annotation<string>,

    // A record object containing dependencies and their versions extracted from the file.
    dependencies: Annotation<Record<string, string>>,

    // An array of strings listing the identified vulnerabilities in the dependencies.
    vulnerabilities: Annotation<string[]>,

    // An array of strings providing secure and up-to-date alternatives for vulnerable dependencies.
    alternatives: Annotation<string[]>,

    // A string representing the file path where the generated report is saved.
    reportPath: Annotation<string>,
});


 

