import { ChatOpenAI } from "@langchain/openai";
import { DependencyRiskAnnotation } from "../state";

export async function secureAlternatives(
  state: typeof DependencyRiskAnnotation.State
): Promise<{ alternatives: string[] }> {
  console.log(`Running secureAlternatives`);
  const llm = new ChatOpenAI({
    openAIApiKey: process.env.OPENROUTER_API_KEY,
    configuration: {
      baseURL: "https://openrouter.ai/api/v1",
      apiKey: process.env.OPENROUTER_API_KEY,
    },
    modelName: "google/gemini-2.5-pro-preview",
  });
  const alternatives: string[] = [];

  for (const [dependency, version] of Object.entries(state.dependencies)) {
    try {
      const prompt = `Suggest a secure and up-to-date alternative for the following dependency: Dependency: ${dependency} Version: ${version}`;
      const response = await llm.invoke(prompt);
      alternatives.push(response.content.toString());
    } catch (error) {
      console.error("Error generating response:", error);
      throw new Error("Failed to generate response from <PERSON>");
    }
  }

  return { alternatives };
}
