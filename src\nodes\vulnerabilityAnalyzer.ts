import { HumanMessage } from "@langchain/core/messages";

import { DependencyRiskAnnotation } from "../state";
import { ChatOpenAI } from "@langchain/openai";

export async function vulnerabilityAnalyzer(
  state: typeof DependencyRiskAnnotation.State
): Promise<{ vulnerabilities: string[] }> {
  console.log(`Running vulnerabilityAnalyzer`, state);

  const llm = new ChatOpenAI({
    openAIApiKey: process.env.OPENROUTER_API_KEY,
    configuration: {
      baseURL: "https://openrouter.ai/api/v1",
      apiKey: process.env.OPENROUTER_API_KEY,
    },
    modelName: "google/gemini-2.5-pro-preview",
  });

  const vulnerabilities: string[] = [];

  for (const [dependency, version] of Object.entries(state.dependencies)) {
    try {
      const input = [
        new HumanMessage({
          content: [
            {
              type: "text",
              text: "You are a security analyst for software dependencies.",
              role: "system",
            },
            {
              type: "text",
              text: `Analyze the dependency "${dependency}" (version "${version}") only for known vulnerabilities.`,
              role: "user",
            },
          ],
        }),
      ];

      const response = await llm.invoke(input);
      vulnerabilities.push(response.content.toString());
    } catch (error) {
      console.error("Error generating response:", error);
      throw new Error("Failed to generate response from Gemini");
    }
  }
  return { vulnerabilities };
}
