import { StateGraph, START, END, Send } from "@langchain/langgraph";
import { dependencyScanner } from "./nodes/dependencyScanner";
import { vulnerabilityAnalyzer } from "./nodes/vulnerabilityAnalyzer";
import { secureAlternatives } from "./nodes/secureAlternatives";
import { reportGenerator } from "./nodes/reportGenerator";
import { DependencyRiskAnnotation } from "./state";
import * as dotenv from 'dotenv';
import { HumanMessage } from "@langchain/core/messages";
import { ChatOpenAI } from "@langchain/openai";
dotenv.config();

// Routing function that determines the next step in the workflow based on the current state.
// This function checks the state object to decide which node (agent) to execute next.
function routingFunction(state: typeof DependencyRiskAnnotation.State) {
    // If the 'dependencies' object is not empty, proceed to the 'vulnerabilityAnalyzer' node.
    if (Object.entries(state.dependencies).length !== 0) {
        return "vulnerabilityAnalyzer";
    } else {
        // If there are no dependencies, end the workflow.
        return END;
    }
}


// We will use this an edge in the graph
export const continueToChapters = (state: typeof DependencyRiskAnnotation.State
) => {
    // We will return a list of `Send` objects
    // Each `Send` object consists of the name of a node in the graph
    // as well as the state to send to that node
    return state.dependencies   .map((subject) => new Send("chapterFanOut", { subject }));
  };

function combine_node(state: typeof DependencyRiskAnnotation.State) {
    return {
        ...state,
        vulnerabilities: state.vulnerabilities,
        alternatives: state.alternatives,
    };
}



export async function vulnerability(  state: typeof DependencyRiskAnnotation.State): Promise<{ vulnerabilities: string[] }> {
  console.log(`Running vulnerabilityAnalyzer`, state);

  const llm = new ChatOpenAI({
    openAIApiKey: process.env.OPENROUTER_API_KEY,
    configuration: {
      baseURL: "https://openrouter.ai/api/v1",
      apiKey: process.env.OPENROUTER_API_KEY,
    },
    modelName: "google/gemini-2.5-pro-preview",
  });

  const vulnerabilities: string[] = [];

  for (const [dependency, version] of Object.entries(state.dependencies)) {
    try {
      const input = [
        new HumanMessage({
          content: [
            {
              type: "text",
              text: "You are a security analyst for software dependencies.",
              role: "system",
            },
            {
              type: "text",
              text: `Analyze the dependency "${dependency}" (version "${version}") only for known vulnerabilities.`,
              role: "user",
            },
          ],
        }),
      ];

      const response = await llm.invoke(input);
      vulnerabilities.push(response.content.toString());
    } catch (error) {
      console.error("Error generating response:", error);
      throw new Error("Failed to generate response from Gemini");
    }
  }
  return { vulnerabilities };
}


const builder = new StateGraph(DependencyRiskAnnotation)
        //adding graph nodes
        .addNode('dependencyScanner', dependencyScanner)
        .addNode("vulnerabilityAnalyzer", vulnerabilityAnalyzer)
        .addNode("secureAlternatives", secureAlternatives)
        .addNode("reportGenerator", reportGenerator)
        //adding edges between nodes
        .addEdge(START, "dependencyScanner")
        //here we use a routing function to enable graph decide the next node based on the state
        .addConditionalEdges("dependencyScanner", routingFunction, [END, "vulnerabilityAnalyzer"])
        .addEdge("vulnerabilityAnalyzer", "secureAlternatives")
        .addEdge("secureAlternatives", "reportGenerator")
        .addEdge("reportGenerator", END);


    // Graph needs to be compiled before run    
export const graph = builder.compile();


// async function main() {

//     // const builder = new StateGraph(DependencyRiskAnnotation)
//     //     //adding graph nodes
//     //     .addNode('dependencyScanner', dependencyScanner)
//     //     .addNode("vulnerabilityAnalyzer", vulnerabilityAnalyzer)
//     //     .addNode("secureAlternatives", secureAlternatives)
//     //     .addNode("reportGenerator", reportGenerator)
//     //     //adding edges between nodes
//     //     .addEdge(START, "dependencyScanner")
//     //     //here we use a routing function to enable graph decide the next node based on the state
//     //     .addConditionalEdges("dependencyScanner", routingFunction, [END, "vulnerabilityAnalyzer"])
//     //     .addEdge("vulnerabilityAnalyzer", "secureAlternatives")
//     //     .addEdge("secureAlternatives", "reportGenerator")
//     //     .addEdge("reportGenerator", END);



//     const agentResult = await securityGraph.invoke({ filePath: './package.json' });
//     console.log(agentResult.reportPath);
// }

//main().catch(console.error); 